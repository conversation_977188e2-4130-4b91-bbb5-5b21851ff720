{"kind": "collectionType", "collectionName": "articles", "info": {"singularName": "article", "pluralName": "articles", "displayName": "Article", "description": "A simple article content type"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "content": {"type": "richtext", "required": true}, "slug": {"type": "uid", "targetField": "title", "required": true}, "excerpt": {"type": "text", "maxLength": 500}, "featured_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "author": {"type": "string", "required": true}, "tags": {"type": "json"}, "meta_title": {"type": "string", "maxLength": 60}, "meta_description": {"type": "text", "maxLength": 160}}}