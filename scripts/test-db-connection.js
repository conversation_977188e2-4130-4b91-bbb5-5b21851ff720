#!/usr/bin/env node

const { Client } = require('pg');

// Database configuration
const dbConfig = {
  host: process.env.DATABASE_HOST || 'twostapp-dev.chk68km08hmu.ca-central-1.rds.amazonaws.com',
  port: process.env.DATABASE_PORT || 5432,
  database: process.env.DATABASE_NAME || 'twostapp',
  user: process.env.DATABASE_USERNAME || 'sysadmin',
  password: process.env.DATABASE_PASSWORD || 'z9PtOUCN.1pKkBI8p0ieO7ho7qmCV2',
  ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false
};

async function testConnection() {
  const client = new Client(dbConfig);
  
  try {
    console.log('Testing PostgreSQL connection...');
    console.log(`Host: ${dbConfig.host}`);
    console.log(`Port: ${dbConfig.port}`);
    console.log(`Database: ${dbConfig.database}`);
    console.log(`User: ${dbConfig.user}`);
    
    await client.connect();
    console.log('✅ Connection successful!');

    // Test a simple query
    const result = await client.query('SELECT version()');
    console.log(`\\nPostgreSQL Version: ${result.rows[0].version}`);

    // Check if database exists and list tables
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);

    console.log(`\\nExisting tables (${tablesResult.rows.length}):`);
    if (tablesResult.rows.length === 0) {
      console.log('No tables found. Database is empty.');
    } else {
      tablesResult.rows.forEach(row => {
        console.log(`- ${row.table_name}`);
      });
    }

  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    
    if (error.code === 'ENOTFOUND') {
      console.error('Host not found. Check your DATABASE_HOST configuration.');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('Connection refused. Check if PostgreSQL is running and accessible.');
    } else if (error.code === '28P01') {
      console.error('Authentication failed. Check your username and password.');
    } else if (error.code === '3D000') {
      console.error('Database does not exist. Check your DATABASE_NAME configuration.');
    }
    
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Check if this script is being run directly
if (require.main === module) {
  testConnection().catch(console.error);
}

module.exports = { testConnection };
