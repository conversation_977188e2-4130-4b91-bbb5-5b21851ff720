#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// Database configuration
const dbConfig = {
  host: process.env.DATABASE_HOST || 'twostapp-dev.chk68km08hmu.ca-central-1.rds.amazonaws.com',
  port: process.env.DATABASE_PORT || 5432,
  database: process.env.DATABASE_NAME || 'twostapp',
  user: process.env.DATABASE_USERNAME || 'sysadmin',
  password: process.env.DATABASE_PASSWORD || 'z9PtOUCN.1pKkBI8p0ieO7ho7qmCV2',
  ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false
};

async function runMigration() {
  const client = new Client(dbConfig);
  
  try {
    console.log('Connecting to PostgreSQL database...');
    await client.connect();
    console.log('Connected successfully!');

    // Read the migration file
    const migrationPath = path.join(__dirname, 'postgres-schema.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Running migration...');
    await client.query(migrationSQL);
    console.log('Migration completed successfully!');

    // Verify tables were created
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);

    console.log('\\nCreated tables:');
    result.rows.forEach(row => {
      console.log(`- ${row.table_name}`);
    });

  } catch (error) {
    console.error('Migration failed:', error.message);
    process.exit(1);
  } finally {
    await client.end();
    console.log('\\nDatabase connection closed.');
  }
}

// Check if this script is being run directly
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = { runMigration };
