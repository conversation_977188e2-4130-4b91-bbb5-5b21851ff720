{"name": "my-project", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "db:test": "node scripts/test-db-connection.js", "db:migrate": "node scripts/migrate-database.js"}, "dependencies": {"@strapi/plugin-cloud": "5.21.0", "@strapi/plugin-users-permissions": "5.21.0", "@strapi/strapi": "5.21.0", "better-sqlite3": "11.3.0", "pg": "^8.16.3", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "0656401988a1f69891781d0932f72c1eea401b5226e6bba340d8d34c55995234"}}