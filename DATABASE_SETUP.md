# Strapi Database Schema Setup

This document explains how the database schema has been created for your Strapi project.

## Current Setup

### Database Configuration
- **Current Database**: SQLite (for development)
- **Database File**: `.tmp/data.db`
- **Production Database**: PostgreSQL (configured but not active)

### Created Content Types

#### Article Content Type
Location: `src/api/article/content-types/article/schema.json`

**Fields:**
- `title` (string, required, max 255 chars)
- `content` (richtext, required)
- `slug` (uid, auto-generated from title, required)
- `excerpt` (text, max 500 chars)
- `featured_image` (media, single image)
- `author` (string, required)
- `tags` (json)
- `meta_title` (string, max 60 chars)
- `meta_description` (text, max 160 chars)

**Features:**
- Draft & Publish enabled
- Auto-generated slug from title
- SEO-friendly meta fields
- Media support for featured images

## Database Schema Status

✅ **SQLite Schema Created** - Strapi is running with SQLite and has automatically created all necessary tables including:
- Core Strapi tables (users, permissions, files, etc.)
- Article content type table
- Relation tables for media associations

## Scripts Available

### Database Connection Testing
```bash
npm run db:test
```
Tests the PostgreSQL connection using the credentials in your `.env` file.

### PostgreSQL Migration
```bash
npm run db:migrate
```
Runs the PostgreSQL schema creation script to set up all tables in PostgreSQL.

## Files Created

### Content Type Files
- `src/api/article/content-types/article/schema.json` - Content type definition
- `src/api/article/controllers/article.ts` - API controller
- `src/api/article/services/article.ts` - Business logic service
- `src/api/article/routes/article.ts` - API routes

### Database Scripts
- `scripts/test-db-connection.js` - PostgreSQL connection tester
- `scripts/migrate-database.js` - PostgreSQL migration runner
- `scripts/postgres-schema.sql` - Complete PostgreSQL schema

### Configuration
- `config/database.ts` - Database configuration (supports both SQLite and PostgreSQL)
- `.env` - Environment variables (currently set to SQLite)

## Next Steps

### 1. Access Strapi Admin
Visit: http://localhost:1337/admin
- Create your first administrator account
- Explore the Article content type in the Content Manager
- Add some sample articles

### 2. Switch to PostgreSQL (when ready)
1. Update `.env` file:
   ```env
   DATABASE_CLIENT=postgres
   DATABASE_HOST=twostapp-dev.chk68km08hmu.ca-central-1.rds.amazonaws.com
   DATABASE_PORT=5432
   DATABASE_NAME=twostapp
   DATABASE_USERNAME=sysadmin
   DATABASE_PASSWORD=z9PtOUCN.1pKkBI8p0ieO7ho7qmCV2
   DATABASE_SSL=false
   DATABASE_SCHEMA=public
   ```

2. Test the connection:
   ```bash
   npm run db:test
   ```

3. Run the migration:
   ```bash
   npm run db:migrate
   ```

4. Restart Strapi:
   ```bash
   npm run develop
   ```

### 3. API Endpoints
Once running, your Article API will be available at:
- `GET /api/articles` - List all articles
- `GET /api/articles/:id` - Get specific article
- `POST /api/articles` - Create new article
- `PUT /api/articles/:id` - Update article
- `DELETE /api/articles/:id` - Delete article

## Database Schema Details

The PostgreSQL schema includes:
- **Core Strapi Tables**: Authentication, permissions, file management
- **User Management**: Admin users and public users with roles
- **Content Management**: Articles with full CRUD operations
- **Media Management**: File uploads and associations
- **SEO Support**: Meta fields for search optimization
- **Performance**: Optimized indexes on key fields

## Troubleshooting

### SQLite Issues
- Database file is created automatically in `.tmp/data.db`
- Delete the file to reset the database
- No external dependencies required

### PostgreSQL Issues
- Use `npm run db:test` to verify connection
- Check network connectivity to AWS RDS
- Verify credentials in `.env` file
- Ensure PostgreSQL service is running

### Content Type Issues
- Restart Strapi after schema changes
- Check the admin panel for content type registration
- Verify file structure in `src/api/article/`

## Security Notes

⚠️ **Important**: The database credentials in this setup are currently hardcoded. For production:
1. Use environment variables for all sensitive data
2. Enable SSL for PostgreSQL connections
3. Use strong, unique passwords
4. Implement proper access controls
5. Regular security updates
